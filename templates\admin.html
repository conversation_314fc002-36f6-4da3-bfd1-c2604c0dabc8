<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - NewsBalancer</title>

    <!-- Unified CSS System -->
    <link rel="stylesheet" href="/static/css/app-consolidated.css?v=1" />

    <!-- HTMX for dynamic content loading -->
    <script src="https://unpkg.com/htmx.org@1.9.10"
            integrity="sha384-D1Kt99CQMDuVetoL1lrYwg5t+9QdHe7NLX/SoJYkXDFfX9o7wOvkiMvGGce9WHb8"
            crossorigin="anonymous"></script>

</head>
<body>    <header class="navbar" role="banner">
        <div class="container">
            <a href="/articles" class="navbar-brand">NewsBalancer Admin</a>
            <nav class="navbar-nav" role="navigation" aria-label="Main navigation">
                <a href="/articles">Articles</a>
                <a href="/admin" aria-current="page" class="active">Admin</a>
            </nav>
        </div>
    </header>

    <div class="container">
        <h1>Admin Dashboard</h1>

        <!-- System Status -->
        <div class="system-status">
            <div class="status-item {{if .SystemStatus.DatabaseOK}}status-good{{else}}status-error{{end}}">
                <h4>Database</h4>
                <p>{{if .SystemStatus.DatabaseOK}}✅ Connected{{else}}❌ Error{{end}}</p>
            </div>
            <div class="status-item {{if .SystemStatus.LLMServiceOK}}status-good{{else}}status-warning{{end}}">
                <h4>LLM Service</h4>
                <p>{{if .SystemStatus.LLMServiceOK}}✅ Available{{else}}⚠️ Unavailable{{end}}</p>
            </div>
            <div class="status-item {{if .SystemStatus.RSSServiceOK}}status-good{{else}}status-warning{{end}}">
                <h4>RSS Feeds</h4>
                <p>{{if .SystemStatus.RSSServiceOK}}✅ Active{{else}}⚠️ Issues{{end}}</p>
            </div>
            <div class="status-item {{if .SystemStatus.ServerOK}}status-good{{else}}status-warning{{end}}">
                <h4>Server</h4>
                <p>{{if .SystemStatus.ServerOK}}✅ Running{{else}}⚠️ Issues{{end}}</p>
            </div>
        </div>

        <!-- Main Dashboard -->
        <div class="admin-dashboard">
            <div class="dashboard-stats equal-columns-layout">
            <!-- Statistics Card -->
            <div class="dashboard-card">
                <h3>System Statistics</h3>
                <div class="stat-item">
                    <span>Total Articles:</span>
                    <span class="stat-value">{{.Stats.TotalArticles}}</span>
                </div>
                <div class="stat-item">
                    <span>Articles Today:</span>
                    <span class="stat-value">{{.Stats.ArticlesToday}}</span>
                </div>
                <div class="stat-item">
                    <span>Pending Analysis:</span>
                    <span class="stat-value">{{.Stats.PendingAnalysis}}</span>
                </div>
                <div class="stat-item">
                    <span>Active Sources:</span>
                    <span class="stat-value">{{.Stats.ActiveSources}}</span>
                </div>
                <div class="stat-item">
                    <span>Database Size:</span>
                    <span class="stat-value">{{.Stats.DatabaseSize}}</span>
                </div>
            </div>

            <!-- Bias Distribution Card -->
            <div class="dashboard-card">
                <h3>Bias Distribution</h3>
                <div class="bias-distribution">
                    <div class="bias-stat bias-left">
                        <div class="value">{{.Stats.LeftCount}}</div>
                        <div class="label">Left Leaning</div>
                        <div class="label">({{.Stats.LeftPercentage}}%)</div>
                    </div>
                    <div class="bias-stat bias-center">
                        <div class="value">{{.Stats.CenterCount}}</div>
                        <div class="label">Center</div>
                        <div class="label">({{.Stats.CenterPercentage}}%)</div>
                    </div>
                    <div class="bias-stat bias-right">
                        <div class="value">{{.Stats.RightCount}}</div>
                        <div class="label">Right Leaning</div>
                        <div class="label">({{.Stats.RightPercentage}}%)</div>
                    </div>
                </div>
            </div>
            </div>

            <!-- Admin Controls -->
        <div class="admin-controls">
            <h3>System Controls</h3>

            <div class="control-section">
                <h4>Feed Management</h4>
                <div class="btn-group">
                    <button class="btn btn-primary" onclick="refreshFeeds()">Refresh All Feeds</button>
                    <button class="btn btn-warning" onclick="resetFeedErrors()">Reset Feed Errors</button>
                    <a href="/api/admin/sources" class="btn btn-info" target="_blank">View Sources Status</a>
                </div>
            </div>

            <div class="control-section">
                <h4>Analysis Control</h4>
                <div class="btn-group">
                    <button class="btn btn-primary" onclick="reanalyzeArticles()">Reanalyze Recent Articles</button>
                    <button class="btn btn-warning" onclick="clearAnalysisErrors()">Clear Analysis Errors</button>
                    <button class="btn btn-success" onclick="validateBiasScores()">Validate Bias Scores</button>
                </div>
            </div>

            <div class="control-section">
                <h4>Database Management</h4>
                <div class="btn-group">
                    <button class="btn btn-warning" onclick="optimizeDatabase()">Optimize Database</button>
                    <button class="btn btn-info" onclick="exportData()">Export Data</button>
                    <button class="btn btn-danger" onclick="confirmCleanupOldArticles()" title="Remove articles older than 30 days">Cleanup Old Articles</button>
                </div>
            </div>

            <div class="control-section">
                <h4>Monitoring</h4>
                <div class="btn-group">
                    <a href="/api/admin/metrics" class="btn btn-info" target="_blank">View Metrics</a>
                    <a href="/api/admin/logs" class="btn btn-info" target="_blank">View Logs</a>
                    <button class="btn btn-success" onclick="runHealthCheck()">Run Health Check</button>
                </div>
            </div>
        </div>

        <!-- Source Management -->
        <div class="source-management-section">
            <div id="source-list-container"
                 hx-get="/htmx/sources"
                 hx-trigger="load"
                 hx-swap="innerHTML"
                 data-testid="source-management-container">
                <div class="loading">Loading sources...</div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="recent-activity">
            <h3>Recent Activity</h3>
            {{range .RecentActivity}}
            <div class="activity-item">
                <div>{{.Message}}</div>
                <div class="activity-time">{{.Timestamp}}</div>
            </div>
            {{else}}
            <p>No recent activity recorded.</p>
            {{end}}
        </div>
        </div>
    </div>

    <script>
        // Admin control functions
        function refreshFeeds() {
            if (confirm('Refresh all RSS feeds? This may take a few minutes.')) {
                fetch('/api/admin/refresh-feeds', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        alert('Feed refresh initiated. Check logs for progress.');
                        location.reload();
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Error initiating feed refresh');
                    });
            }
        }

        function reanalyzeArticles() {
            if (confirm('Reanalyze recent articles? This will use LLM credits.')) {
                fetch('/api/admin/reanalyze-recent', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        alert('Reanalysis initiated. Check logs for progress.');
                        location.reload();
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Error initiating reanalysis');
                    });
            }
        }

        function optimizeDatabase() {
            if (confirm('Optimize database? This may take a few minutes.')) {
                fetch('/api/admin/optimize-db', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        alert('Database optimization completed.');
                        location.reload();
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Error optimizing database');
                    });
            }
        }

        function confirmCleanupOldArticles() {
            if (confirm('Delete articles older than 30 days? This action cannot be undone.')) {
                if (confirm('Are you absolutely sure? This will permanently delete old articles.')) {
                    fetch('/api/admin/cleanup-old', { method: 'DELETE' })
                        .then(response => response.json())
                        .then(data => {
                            alert(`Cleanup completed. Removed ${data.deletedCount} articles.`);
                            location.reload();
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('Error during cleanup');
                        });
                }
            }
        }

        function resetFeedErrors() {
            fetch('/api/admin/reset-feed-errors', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    alert('Feed errors reset.');
                    location.reload();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error resetting feed errors');
                });
        }

        function clearAnalysisErrors() {
            fetch('/api/admin/clear-analysis-errors', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    alert('Analysis errors cleared.');
                    location.reload();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error clearing analysis errors');
                });
        }

        function validateBiasScores() {
            fetch('/api/admin/validate-scores', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    alert('Score validation completed. Check logs for details.');
                    location.reload();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error validating scores');
                });
        }

        function exportData() {
            window.location.href = '/api/admin/export';
        }

        function runHealthCheck() {
            fetch('/api/admin/health-check', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    alert('Health check completed. All systems operational.');
                    location.reload();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Health check failed');
                });
        }

        // Source Management Functions
        function clearSourceForm() {
            document.getElementById('source-form-container').innerHTML = '';
        }

        function showSourceStatsModal() {
            document.getElementById('source-stats-modal').style.display = 'block';
        }

        function hideSourceStatsModal() {
            document.getElementById('source-stats-modal').style.display = 'none';
        }

        // Close modal when clicking outside of it
        window.onclick = function(event) {
            const modal = document.getElementById('source-stats-modal');
            if (event.target === modal) {
                hideSourceStatsModal();
            }
        }

        // HTMX event handlers for source management
        document.body.addEventListener('htmx:afterRequest', function(event) {
            if (event.detail.xhr.status === 200 || event.detail.xhr.status === 201) {
                // Refresh source list after successful operations
                if (event.detail.requestConfig.path.includes('/api/sources')) {
                    htmx.trigger('#source-list-container', 'refresh');
                }
            }
        });

        document.body.addEventListener('htmx:responseError', function(event) {
            const response = JSON.parse(event.detail.xhr.responseText);
            alert('Error: ' + (response.error?.message || 'Unknown error occurred'));
        });
    </script>
</body>
</html>
